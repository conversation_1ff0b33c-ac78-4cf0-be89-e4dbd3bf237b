import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'main.dart';

class SimpleGuestTest extends StatefulWidget {
  const SimpleGuestTest({super.key});

  @override
  State<SimpleGuestTest> createState() => _SimpleGuestTestState();
}

class _SimpleGuestTestState extends State<SimpleGuestTest> {
  bool _isLoggedIn = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'اختبار تسجيل الدخول البسيط',
          style: GoogleFonts.cairo(),
        ),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'حالة تسجيل الدخول:',
                      style: GoogleFonts.cairo(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 10),
                    Text(
                      _isLoggedIn ? '✅ مسجل دخول كضيف' : '❌ غير مسجل دخول',
                      style: GoogleFonts.cairo(fontSize: 16),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 30),
            if (!_isLoggedIn) ...[
              ElevatedButton(
                onPressed: _simulateGuestLogin,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: Text(
                  'تسجيل الدخول كضيف (محاكاة)',
                  style: GoogleFonts.cairo(fontSize: 16),
                ),
              ),
            ] else ...[
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pushReplacement(
                    MaterialPageRoute(builder: (context) => const MainScreen()),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: Text(
                  'الذهاب للتطبيق الرئيسي',
                  style: GoogleFonts.cairo(fontSize: 16),
                ),
              ),
              const SizedBox(height: 10),
              ElevatedButton(
                onPressed: _simulateLogout,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: Text(
                  'تسجيل الخروج',
                  style: GoogleFonts.cairo(fontSize: 16),
                ),
              ),
            ],
            const SizedBox(height: 30),
            Text(
              'هذا اختبار بسيط لمحاكاة تسجيل الدخول كضيف بدون Firebase',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _simulateGuestLogin() {
    setState(() {
      _isLoggedIn = true;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'تم تسجيل الدخول كضيف بنجاح (محاكاة)',
          style: GoogleFonts.cairo(),
        ),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _simulateLogout() {
    setState(() {
      _isLoggedIn = false;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'تم تسجيل الخروج بنجاح',
          style: GoogleFonts.cairo(),
        ),
        backgroundColor: Colors.orange,
      ),
    );
  }
}
