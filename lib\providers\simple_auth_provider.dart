import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../services/email_verification_service.dart';

class SimpleAuthProvider extends ChangeNotifier {
  User? _firebaseUser;
  bool _isLoading = false;
  String? _error;
  bool _isInitialized = false;

  // Getters
  User? get firebaseUser => _firebaseUser;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isInitialized => _isInitialized;
  bool get isLoggedIn => _firebaseUser != null;
  bool get isGuest => _firebaseUser?.isAnonymous ?? false;
  String? get currentUserId => _firebaseUser?.uid;
  String get displayName => _firebaseUser?.displayName ?? 'ضيف';

  SimpleAuthProvider() {
    _initializeAuth();
  }

  // تهيئة المصادقة
  void _initializeAuth() async {
    try {
      // التحقق من المستخدم الحالي
      _firebaseUser = FirebaseAuth.instance.currentUser;
      _isInitialized = true;
      notifyListeners();

      // الاستماع لتغييرات حالة المصادقة
      FirebaseAuth.instance.authStateChanges().listen((User? user) async {
        _firebaseUser = user;
        notifyListeners();
      });
    } catch (e) {
      _setError('خطأ في تهيئة المصادقة: $e');
      _isInitialized = true; // تعيين true حتى لو حدث خطأ
      notifyListeners();
    }
  }

  // تسجيل الدخول كضيف مباشرة
  Future<bool> signInAsGuest() async {
    return await _performAuth(() async {
      try {
        // تسجيل الدخول كضيف مباشرة بدون خدمات إضافية
        final UserCredential userCredential =
            await FirebaseAuth.instance.signInAnonymously();

        if (userCredential.user != null) {
          // تحديث اسم المستخدم ليظهر كضيف
          await userCredential.user!.updateDisplayName('ضيف');
          await userCredential.user!.reload();
          return true;
        }
        return false;
      } on FirebaseAuthException catch (e) {
        switch (e.code) {
          case 'operation-not-allowed':
            _setError('تسجيل الدخول كضيف غير مفعل في هذا التطبيق');
            break;
          case 'network-request-failed':
            _setError('مشكلة في الاتصال بالإنترنت. يرجى المحاولة مرة أخرى');
            break;
          default:
            _setError('فشل في تسجيل الدخول كضيف: ${e.message}');
        }
        return false;
      } catch (e) {
        _setError('حدث خطأ غير متوقع: $e');
        return false;
      }
    }, 'تسجيل الدخول كضيف');
  }

  // دالة إرسال كود التحقق من البريد الإلكتروني
  Future<String?> sendEmailVerificationCode() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null && user.email != null && !user.emailVerified) {
        final code = await EmailVerificationService.sendVerificationCode(
          user.email!,
        );
        if (code != null) {
          return code;
        }
      }
      return null;
    } catch (e) {
      _setError('فشل إرسال كود التحقق: $e');
      return null;
    }
  }

  // دالة التحقق من كود التحقق
  Future<bool> verifyEmailCode(String code) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null && user.email != null) {
        final result = await EmailVerificationService.verifyCode(
          user.email!,
          code,
        );
        if (result.success) {
          // تحديث حالة التحقق في Firebase Auth (محاكاة)
          // في التطبيق الحقيقي، يتم هذا عبر Firebase Functions
          await user.reload();
          _firebaseUser = FirebaseAuth.instance.currentUser;
          notifyListeners();
          return true;
        } else {
          _setError(result.message);
          return false;
        }
      }
      return false;
    } catch (e) {
      _setError('فشل التحقق من الكود: $e');
      return false;
    }
  }

  // دالة إعادة تحميل بيانات المستخدم
  Future<void> reloadUser() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        await user.reload();
        _firebaseUser = FirebaseAuth.instance.currentUser;
        notifyListeners();
      }
    } catch (e) {
      _setError('فشل إعادة تحميل البيانات: $e');
    }
  }

  // تسجيل الخروج
  Future<bool> signOut() async {
    try {
      _setLoading(true);
      _clearError();

      // تسجيل الخروج من Google أيضاً إذا كان مسجل دخول
      try {
        final GoogleSignIn googleSignIn = GoogleSignIn();
        if (await googleSignIn.isSignedIn()) {
          await googleSignIn.signOut();
        }
      } catch (e) {
        // تجاهل أخطاء Google Sign Out
      }

      // تسجيل الخروج من Firebase
      await FirebaseAuth.instance.signOut();

      // تحديث حالة المستخدم
      _firebaseUser = null;
      notifyListeners();

      return true;
    } catch (e) {
      _setError('فشل في تسجيل الخروج: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // دالة مساعدة لتنفيذ العمليات
  Future<bool> _performAuth(
    Future<bool> Function() operation,
    String operationName,
  ) async {
    try {
      _setLoading(true);
      _clearError();

      final success = await operation();

      if (!success && _error == null) {
        _setError('فشل في $operationName');
      }

      return success;
    } catch (e) {
      if (_error == null) {
        _setError('خطأ في $operationName: $e');
      }
      return false;
    } finally {
      // تحديث حالة المستخدم أولاً
      _firebaseUser = FirebaseAuth.instance.currentUser;
      // ثم إيقاف التحميل (سيستدعي notifyListeners تلقائياً)
      _setLoading(false);
    }
  }

  // إدارة حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // إدارة الأخطاء
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }

  // دوال تسجيل الدخول الاجتماعي
  Future<bool> signInWithGoogle() async {
    return await _performAuth(() async {
      try {
        // إنشاء instance من GoogleSignIn
        final GoogleSignIn googleSignIn = GoogleSignIn(
          scopes: ['email', 'profile'],
        );

        // تسجيل الدخول بـ Google
        final GoogleSignInAccount? googleUser = await googleSignIn.signIn();

        if (googleUser == null) {
          // المستخدم ألغى تسجيل الدخول
          return false;
        }

        // الحصول على authentication details
        final GoogleSignInAuthentication googleAuth =
            await googleUser.authentication;

        // إنشاء credential جديد
        final credential = GoogleAuthProvider.credential(
          accessToken: googleAuth.accessToken,
          idToken: googleAuth.idToken,
        );

        // تسجيل الدخول في Firebase باستخدام Google credential
        final UserCredential userCredential = await FirebaseAuth.instance
            .signInWithCredential(credential);

        if (userCredential.user != null) {
          return true;
        }
        return false;
      } on FirebaseAuthException catch (e) {
        switch (e.code) {
          case 'account-exists-with-different-credential':
            _setError(
              'يوجد حساب بهذا البريد الإلكتروني مع طريقة تسجيل دخول مختلفة',
            );
            break;
          case 'invalid-credential':
            _setError('بيانات Google غير صحيحة');
            break;
          case 'operation-not-allowed':
            _setError('تسجيل الدخول بـ Google غير مفعل');
            break;
          case 'user-disabled':
            _setError('تم تعطيل هذا الحساب');
            break;
          case 'user-not-found':
            _setError('لم يتم العثور على الحساب');
            break;
          case 'wrong-password':
            _setError('كلمة المرور غير صحيحة');
            break;
          default:
            _setError('فشل في تسجيل الدخول بـ Google: ${e.message}');
        }
        return false;
      } catch (e) {
        _setError('حدث خطأ غير متوقع: $e');
        return false;
      }
    }, 'تسجيل الدخول بـ Google');
  }

  Future<bool> signInWithFacebook() async {
    return await _performAuth(() async {
      try {
        // تسجيل الدخول بـ Facebook غير مفعل حالياً
        // يمكن تفعيله لاحقاً بإضافة Facebook SDK

        _setError(
          'تسجيل الدخول بـ Facebook غير متوفر حالياً. يرجى استخدام Google أو البريد الإلكتروني.',
        );
        return false;
      } catch (e) {
        _setError('فشل في تسجيل الدخول بـ Facebook: $e');
        return false;
      }
    }, 'تسجيل الدخول بـ Facebook');
  }

  Future<bool> signInWithEmail(String email, String password) async {
    return await _performAuth(() async {
      try {
        final UserCredential userCredential = await FirebaseAuth.instance
            .signInWithEmailAndPassword(email: email, password: password);
        return userCredential.user != null;
      } catch (e) {
        if (e is FirebaseAuthException) {
          switch (e.code) {
            case 'user-not-found':
              _setError('لا يوجد حساب مسجل بهذا البريد الإلكتروني');
              break;
            case 'wrong-password':
              _setError('كلمة المرور غير صحيحة');
              break;
            case 'invalid-email':
              _setError('البريد الإلكتروني غير صحيح');
              break;
            case 'user-disabled':
              _setError('تم تعطيل هذا الحساب');
              break;
            default:
              _setError('خطأ في تسجيل الدخول: ${e.message}');
          }
        } else {
          _setError('خطأ غير متوقع في تسجيل الدخول');
        }
        return false;
      }
    }, 'تسجيل الدخول بالإيميل');
  }

  // إنشاء حساب جديد مع Firebase Email Verification
  Future<bool> createAccount(
    String email,
    String password,
    String displayName,
  ) async {
    try {
      _setLoading(true);
      _clearError();

      // إنشاء حساب جديد
      final UserCredential userCredential = await FirebaseAuth.instance
          .createUserWithEmailAndPassword(email: email, password: password);

      if (userCredential.user != null) {
        final user = userCredential.user!;

        // تحديث اسم المستخدم
        if (displayName.isNotEmpty) {
          await user.updateDisplayName(displayName);
          await user.reload();
        }

        // إرسال رسالة التحقق من Firebase
        await user.sendEmailVerification();

        // حفظ بيانات المستخدم في Firestore
        await _saveUserToFirestore(user.uid, displayName, email);

        // تسجيل خروج فوري - المستخدم لا يستطيع الدخول حتى يتحقق من بريده
        await FirebaseAuth.instance.signOut();
        _firebaseUser = null;

        return true;
      }

      return false;
    } on FirebaseAuthException catch (e) {
      switch (e.code) {
        case 'email-already-in-use':
          _setError(
            'يوجد حساب مسجل بهذا البريد الإلكتروني بالفعل. يرجى تسجيل الدخول.',
          );
          break;
        case 'weak-password':
          _setError(
            'كلمة المرور ضعيفة. يرجى اختيار كلمة مرور أقوى (6 أحرف على الأقل).',
          );
          break;
        case 'invalid-email':
          _setError('البريد الإلكتروني غير صحيح.');
          break;
        case 'operation-not-allowed':
          _setError('إنشاء الحسابات غير مفعل حالياً.');
          break;
        case 'network-request-failed':
          _setError('مشكلة في الاتصال بالإنترنت. يرجى المحاولة مرة أخرى.');
          break;
        default:
          _setError('فشل في إنشاء الحساب: ${e.message}');
      }
      return false;
    } catch (e) {
      _setError('حدث خطأ غير متوقع: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // تسجيل الدخول مع التحقق من البريد (إجباري)
  Future<bool> signInWithEmailAndVerification(
    String email,
    String password,
    String? verificationCode,
  ) async {
    return await _performAuth(() async {
      try {
        // التحقق من كود التحقق إجباري
        if (verificationCode == null || verificationCode.trim().isEmpty) {
          _setError(
            'كود التحقق مطلوب. يرجى إدخال الكود المرسل إلى بريدك الإلكتروني.',
          );
          return false;
        }

        // التحقق من صحة كود التحقق
        final verificationResult = await EmailVerificationService.verifyCode(
          email,
          verificationCode.trim(),
        );

        if (!verificationResult.success) {
          _setError(verificationResult.message);
          return false;
        }

        // التحقق نجح - المستخدم مسجل دخول بالفعل
        // نحتاج فقط للتأكد من أن المستخدم الحالي صحيح
        final currentUser = FirebaseAuth.instance.currentUser;
        if (currentUser != null && currentUser.email == email) {
          return true; // التحقق نجح والمستخدم مسجل دخول
        } else {
          // إذا لم يكن هناك مستخدم مسجل دخول، سجل دخول
          final UserCredential userCredential = await FirebaseAuth.instance
              .signInWithEmailAndPassword(email: email, password: password);
          return userCredential.user != null;
        }
      } on FirebaseAuthException catch (e) {
        switch (e.code) {
          case 'user-not-found':
            _setError('لا يوجد حساب مسجل بهذا البريد الإلكتروني');
            break;
          case 'wrong-password':
            _setError('كلمة المرور غير صحيحة');
            break;
          case 'invalid-email':
            _setError('البريد الإلكتروني غير صحيح');
            break;
          case 'user-disabled':
            _setError('تم تعطيل هذا الحساب');
            break;
          case 'too-many-requests':
            _setError('محاولات كثيرة. يرجى المحاولة لاحقاً');
            break;
          case 'network-request-failed':
            _setError('مشكلة في الاتصال بالإنترنت');
            break;
          case 'invalid-credential':
            _setError('بيانات تسجيل الدخول غير صحيحة');
            break;
          default:
            _setError('فشل في تسجيل الدخول: ${e.message}');
        }
        return false;
      } catch (e) {
        _setError('حدث خطأ غير متوقع: $e');
        return false;
      }
    }, 'تسجيل الدخول مع التحقق');
  }

  // حفظ بيانات المستخدم في Firestore
  Future<void> _saveUserToFirestore(
    String uid,
    String displayName,
    String email,
  ) async {
    try {
      await FirebaseFirestore.instance.collection('users').doc(uid).set({
        'uid': uid,
        'displayName': displayName,
        'email': email,
        'createdAt': FieldValue.serverTimestamp(),
        'emailVerified': false,
        'lastLoginAt': null,
      });
    } catch (e) {
      // لا نرمي خطأ هنا لأن إنشاء الحساب نجح
    }
  }

  // تحديث آخر تسجيل دخول في Firestore
  Future<void> _updateLastLogin(String uid) async {
    try {
      await FirebaseFirestore.instance.collection('users').doc(uid).update({
        'lastLoginAt': FieldValue.serverTimestamp(),
        'emailVerified': true,
      });
    } catch (e) {
      // تجاهل أخطاء التحديث
    }
  }

  // تسجيل الدخول مع التحقق من تفعيل البريد
  Future<bool> signInWithEmailOnly(String email, String password) async {
    return await _performAuth(() async {
      try {
        // تسجيل الدخول
        final UserCredential userCredential = await FirebaseAuth.instance
            .signInWithEmailAndPassword(email: email, password: password);

        if (userCredential.user != null) {
          final user = userCredential.user!;

          // التحقق من تفعيل البريد الإلكتروني
          if (!user.emailVerified) {
            // تسجيل خروج فوري إذا لم يكن البريد مفعل
            await FirebaseAuth.instance.signOut();
            _firebaseUser = null;
            _setError(
              'يرجى التحقق من بريدك الإلكتروني أولاً. تحقق من صندوق الوارد أو الرسائل المزعجة.',
            );
            return false;
          }

          // تحديث آخر تسجيل دخول في Firestore
          await _updateLastLogin(user.uid);

          return true;
        }

        return false;
      } on FirebaseAuthException catch (e) {
        switch (e.code) {
          case 'user-not-found':
            _setError('لا يوجد حساب مسجل بهذا البريد الإلكتروني');
            break;
          case 'wrong-password':
            _setError('كلمة المرور غير صحيحة');
            break;
          case 'invalid-email':
            _setError('البريد الإلكتروني غير صحيح');
            break;
          case 'user-disabled':
            _setError('تم تعطيل هذا الحساب');
            break;
          case 'too-many-requests':
            _setError('محاولات كثيرة. يرجى المحاولة لاحقاً');
            break;
          case 'network-request-failed':
            _setError('مشكلة في الاتصال بالإنترنت');
            break;
          case 'invalid-credential':
            _setError('بيانات تسجيل الدخول غير صحيحة');
            break;
          default:
            _setError('فشل في تسجيل الدخول: ${e.message}');
        }
        return false;
      } catch (e) {
        _setError('حدث خطأ غير متوقع: $e');
        return false;
      }
    }, 'تسجيل الدخول العادي');
  }

  // إعادة تعيين كلمة المرور
  Future<bool> resetPassword(String email) async {
    try {
      _setLoading(true);
      _clearError();

      await FirebaseAuth.instance.sendPasswordResetEmail(email: email);
      return true;
    } on FirebaseAuthException catch (e) {
      switch (e.code) {
        case 'user-not-found':
          _setError('لا يوجد حساب مسجل بهذا البريد الإلكتروني');
          break;
        case 'invalid-email':
          _setError('البريد الإلكتروني غير صحيح');
          break;
        default:
          _setError('فشل في إرسال رابط إعادة تعيين كلمة المرور: ${e.message}');
      }
      return false;
    } catch (e) {
      _setError('حدث خطأ غير متوقع: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // إعادة إرسال رسالة التحقق
  Future<bool> resendEmailVerification() async {
    try {
      _setLoading(true);
      _clearError();

      final user = FirebaseAuth.instance.currentUser;
      if (user != null && !user.emailVerified) {
        await user.sendEmailVerification();
        return true;
      } else {
        _setError('لا يوجد مستخدم أو البريد مفعل بالفعل');
        return false;
      }
    } catch (e) {
      _setError('فشل في إعادة إرسال رسالة التحقق: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // تحديث حالة التحقق من البريد
  Future<bool> checkEmailVerification() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        await user.reload();
        _firebaseUser = FirebaseAuth.instance.currentUser;
        notifyListeners();

        if (user.emailVerified) {
          // تحديث Firestore
          await _updateLastLogin(user.uid);
          return true;
        }
      }
      return false;
    } catch (e) {
      return false;
    }
  }
}
