import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'config/firebase_config.dart';

class TestGuestLogin extends StatefulWidget {
  const TestGuestLogin({super.key});

  @override
  State<TestGuestLogin> createState() => _TestGuestLoginState();
}

class _TestGuestLoginState extends State<TestGuestLogin> {
  String _status = 'جاهز للاختبار';
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار تسجيل الدخول كضيف'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'حالة Firebase:',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 10),
                    Text('عدد التطبيقات: ${Firebase.apps.length}'),
                    Text('المستخدم الحالي: ${FirebaseAuth.instance.currentUser?.uid ?? 'لا يوجد'}'),
                    Text('نوع المستخدم: ${FirebaseAuth.instance.currentUser?.isAnonymous == true ? 'ضيف' : 'مسجل'}'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'حالة الاختبار:',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 10),
                    Text(_status),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 30),
            ElevatedButton(
              onPressed: _isLoading ? null : _testFirebaseInit,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: _isLoading
                  ? const CircularProgressIndicator(color: Colors.white)
                  : const Text('1. اختبار تهيئة Firebase'),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _isLoading ? null : _testGuestLogin,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: _isLoading
                  ? const CircularProgressIndicator(color: Colors.white)
                  : const Text('2. اختبار تسجيل الدخول كضيف'),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _isLoading ? null : _testSignOut,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: _isLoading
                  ? const CircularProgressIndicator(color: Colors.white)
                  : const Text('3. اختبار تسجيل الخروج'),
            ),
            const SizedBox(height: 30),
            if (FirebaseAuth.instance.currentUser != null)
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pushReplacementNamed('/');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('الذهاب للتطبيق الرئيسي'),
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _testFirebaseInit() async {
    setState(() {
      _isLoading = true;
      _status = 'جاري اختبار تهيئة Firebase...';
    });

    try {
      if (Firebase.apps.isEmpty) {
        await FirebaseConfig.initialize();
        setState(() {
          _status = '✅ تم تهيئة Firebase بنجاح!\nعدد التطبيقات: ${Firebase.apps.length}';
        });
      } else {
        setState(() {
          _status = '✅ Firebase مُهيأ مسبقاً!\nعدد التطبيقات: ${Firebase.apps.length}';
        });
      }
    } catch (e) {
      setState(() {
        _status = '❌ خطأ في تهيئة Firebase: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testGuestLogin() async {
    setState(() {
      _isLoading = true;
      _status = 'جاري اختبار تسجيل الدخول كضيف...';
    });

    try {
      // التأكد من تهيئة Firebase أولاً
      if (Firebase.apps.isEmpty) {
        await FirebaseConfig.initialize();
      }

      // تسجيل الدخول كضيف
      final UserCredential userCredential = await FirebaseAuth.instance.signInAnonymously();

      if (userCredential.user != null) {
        setState(() {
          _status = '✅ تم تسجيل الدخول كضيف بنجاح!\n'
              'UID: ${userCredential.user!.uid}\n'
              'Anonymous: ${userCredential.user!.isAnonymous}';
        });
      } else {
        setState(() {
          _status = '❌ فشل في الحصول على بيانات المستخدم';
        });
      }
    } catch (e) {
      setState(() {
        _status = '❌ خطأ في تسجيل الدخول كضيف: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testSignOut() async {
    setState(() {
      _isLoading = true;
      _status = 'جاري اختبار تسجيل الخروج...';
    });

    try {
      await FirebaseAuth.instance.signOut();
      setState(() {
        _status = '✅ تم تسجيل الخروج بنجاح!';
      });
    } catch (e) {
      setState(() {
        _status = '❌ خطأ في تسجيل الخروج: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
