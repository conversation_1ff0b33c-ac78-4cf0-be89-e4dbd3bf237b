<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص Firebase - legal2025</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            direction: ltr;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 فحص Firebase - legal2025</h1>
        
        <div class="loading" id="loading">
            <p>جاري فحص Firebase...</p>
        </div>
        
        <div id="results"></div>
        
        <div style="margin-top: 20px;">
            <button onclick="checkFirebase()">🔄 إعادة الفحص</button>
            <button onclick="testAnonymousAuth()">👤 اختبار تسجيل دخول مجهول</button>
            <button onclick="testEmailAuth()">📧 اختبار إنشاء حساب</button>
            <button onclick="clearResults()">🗑️ مسح النتائج</button>
        </div>
        
        <div style="margin-top: 20px;">
            <h3>معلومات المشروع:</h3>
            <pre>
Project ID: legal2025
Auth Domain: legal2025.firebaseapp.com
Storage Bucket: legal2025.firebasestorage.app
            </pre>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore-compat.js"></script>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyD__fwZYKuehthlJoqGKSDt0YN1TCsPUY8",
            authDomain: "legal2025.firebaseapp.com",
            projectId: "legal2025",
            storageBucket: "legal2025.firebasestorage.app",
            messagingSenderId: "801031214670",
            appId: "1:801031214670:web:a179401f6b476d34db551f"
        };

        // Initialize Firebase
        let app, auth, firestore;
        
        function initFirebase() {
            try {
                app = firebase.initializeApp(firebaseConfig);
                auth = firebase.auth();
                firestore = firebase.firestore();
                return true;
            } catch (error) {
                console.error('خطأ في تهيئة Firebase:', error);
                return false;
            }
        }

        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function showLoading(show = true) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        async function checkFirebase() {
            clearResults();
            showLoading(true);
            
            // تهيئة Firebase
            const initialized = initFirebase();
            if (initialized) {
                addResult('✅ تم تهيئة Firebase بنجاح', 'success');
            } else {
                addResult('❌ فشل في تهيئة Firebase', 'error');
                showLoading(false);
                return;
            }

            // فحص الاتصال
            try {
                await fetch('https://firebase.google.com', { mode: 'no-cors' });
                addResult('✅ الاتصال بـ Firebase متاح', 'success');
            } catch (error) {
                addResult('❌ مشكلة في الاتصال بـ Firebase', 'error');
            }

            // فحص Auth
            try {
                const currentUser = auth.currentUser;
                if (currentUser) {
                    addResult(`✅ مستخدم مسجل دخول: ${currentUser.uid}`, 'success');
                    addResult(`نوع المستخدم: ${currentUser.isAnonymous ? 'مجهول' : 'مسجل'}`, 'info');
                } else {
                    addResult('ℹ️ لا يوجد مستخدم مسجل دخول', 'info');
                }
            } catch (error) {
                addResult(`❌ خطأ في فحص Auth: ${error.message}`, 'error');
            }

            // فحص Firestore
            try {
                await firestore.collection('test').limit(1).get();
                addResult('✅ Firestore متاح ويعمل', 'success');
            } catch (error) {
                addResult(`❌ مشكلة في Firestore: ${error.message}`, 'error');
            }

            showLoading(false);
        }

        async function testAnonymousAuth() {
            showLoading(true);
            try {
                const result = await auth.signInAnonymously();
                addResult(`✅ تم تسجيل الدخول المجهول بنجاح: ${result.user.uid}`, 'success');
            } catch (error) {
                addResult(`❌ فشل تسجيل الدخول المجهول: ${error.message}`, 'error');
                if (error.code === 'auth/operation-not-allowed') {
                    addResult('💡 الحل: فعّل Anonymous Authentication في Firebase Console', 'warning');
                }
            }
            showLoading(false);
        }

        async function testEmailAuth() {
            showLoading(true);
            const testEmail = `test${Date.now()}@test.com`;
            try {
                const result = await auth.createUserWithEmailAndPassword(testEmail, 'test123456');
                addResult(`✅ تم إنشاء حساب بنجاح: ${testEmail}`, 'success');
                // حذف الحساب التجريبي
                await result.user.delete();
                addResult('🗑️ تم حذف الحساب التجريبي', 'info');
            } catch (error) {
                addResult(`❌ فشل إنشاء الحساب: ${error.message}`, 'error');
                if (error.code === 'auth/operation-not-allowed') {
                    addResult('💡 الحل: فعّل Email/Password في Firebase Console', 'warning');
                }
            }
            showLoading(false);
        }

        // تشغيل الفحص عند تحميل الصفحة
        window.onload = function() {
            checkFirebase();
        };
    </script>
</body>
</html>
