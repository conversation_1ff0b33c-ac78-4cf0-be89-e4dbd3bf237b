import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'config/firebase_config.dart';

class FirebaseDiagnostic extends StatefulWidget {
  const FirebaseDiagnostic({super.key});

  @override
  State<FirebaseDiagnostic> createState() => _FirebaseDiagnosticState();
}

class _FirebaseDiagnosticState extends State<FirebaseDiagnostic> {
  Map<String, dynamic> _diagnosticResults = {};
  bool _isRunning = false;

  @override
  void initState() {
    super.initState();
    _runDiagnostics();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تشخيص Firebase', style: GoogleFonts.cairo()),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _runDiagnostics,
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child:
            _isRunning
                ? const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text('جاري فحص Firebase...'),
                    ],
                  ),
                )
                : ListView(
                  children: [
                    _buildSection('🔧 حالة Firebase Core', [
                      _buildResult(
                        'تم التهيئة',
                        _diagnosticResults['coreInitialized'],
                      ),
                      _buildResult(
                        'عدد التطبيقات',
                        _diagnosticResults['appsCount'],
                      ),
                      _buildResult(
                        'اسم التطبيق',
                        _diagnosticResults['appName'],
                      ),
                      _buildResult(
                        'Project ID',
                        _diagnosticResults['projectId'],
                      ),
                    ]),

                    _buildSection('🔐 حالة Firebase Auth', [
                      _buildResult(
                        'Auth متاح',
                        _diagnosticResults['authAvailable'],
                      ),
                      _buildResult(
                        'المستخدم الحالي',
                        _diagnosticResults['currentUser'],
                      ),
                      _buildResult(
                        'نوع المستخدم',
                        _diagnosticResults['userType'],
                      ),
                      _buildResult(
                        'Auth Domain',
                        _diagnosticResults['authDomain'],
                      ),
                    ]),

                    _buildSection('🧪 اختبارات المصادقة', [
                      _buildResult(
                        'تسجيل دخول مجهول',
                        _diagnosticResults['anonymousTest'],
                      ),
                      _buildResult(
                        'إنشاء حساب تجريبي',
                        _diagnosticResults['createAccountTest'],
                      ),
                      _buildResult(
                        'تسجيل خروج',
                        _diagnosticResults['signOutTest'],
                      ),
                    ]),

                    _buildSection('⚙️ إعدادات المنصة', [
                      _buildResult(
                        'المنصة الحالية',
                        _diagnosticResults['currentPlatform'],
                      ),
                      _buildResult('API Key', _diagnosticResults['apiKey']),
                      _buildResult('App ID', _diagnosticResults['appId']),
                    ]),

                    if (_diagnosticResults['errors'] != null) ...[
                      _buildSection('❌ الأخطاء', [
                        for (String error in _diagnosticResults['errors'])
                          _buildError(error),
                      ]),
                    ],

                    const SizedBox(height: 20),
                    ElevatedButton(
                      onPressed: _runDiagnostics,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: Text(
                        'إعادة الفحص',
                        style: GoogleFonts.cairo(fontSize: 16),
                      ),
                    ),
                  ],
                ),
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildResult(String label, dynamic value) {
    Color color = Colors.grey;
    IconData icon = Icons.help;

    if (value is bool) {
      color = value ? Colors.green : Colors.red;
      icon = value ? Icons.check_circle : Icons.error;
    } else if (value is String) {
      color = value.isNotEmpty ? Colors.green : Colors.orange;
      icon = value.isNotEmpty ? Icons.check_circle : Icons.warning;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '$label: ${value?.toString() ?? 'غير متاح'}',
              style: GoogleFonts.cairo(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildError(String error) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(Icons.error, color: Colors.red, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              error,
              style: GoogleFonts.cairo(fontSize: 14, color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _runDiagnostics() async {
    setState(() {
      _isRunning = true;
      _diagnosticResults = {};
    });

    List<String> errors = [];

    try {
      // فحص Firebase Core
      _diagnosticResults['coreInitialized'] = Firebase.apps.isNotEmpty;
      _diagnosticResults['appsCount'] = Firebase.apps.length;

      if (Firebase.apps.isNotEmpty) {
        final app = Firebase.app();
        _diagnosticResults['appName'] = app.name;
        _diagnosticResults['projectId'] = app.options.projectId;
        _diagnosticResults['apiKey'] =
            '${app.options.apiKey.substring(0, 10)}...';
        _diagnosticResults['appId'] =
            '${app.options.appId.substring(0, 15)}...';
      }

      // فحص المنصة
      try {
        final options = FirebaseConfig.currentPlatform;
        _diagnosticResults['currentPlatform'] = 'تم التحديد';
        _diagnosticResults['authDomain'] = options.authDomain ?? 'غير محدد';
      } catch (e) {
        errors.add('خطأ في تحديد المنصة: $e');
        _diagnosticResults['currentPlatform'] = 'خطأ';
      }

      // فحص Firebase Auth
      try {
        final auth = FirebaseAuth.instance;
        _diagnosticResults['authAvailable'] = true;
        _diagnosticResults['currentUser'] = auth.currentUser?.uid ?? 'لا يوجد';
        _diagnosticResults['userType'] =
            auth.currentUser?.isAnonymous == true
                ? 'مجهول'
                : auth.currentUser != null
                ? 'مسجل'
                : 'غير مسجل';
      } catch (e) {
        errors.add('خطأ في Firebase Auth: $e');
        _diagnosticResults['authAvailable'] = false;
      }

      // اختبار تسجيل الدخول المجهول
      try {
        final result = await FirebaseAuth.instance.signInAnonymously();
        _diagnosticResults['anonymousTest'] = result.user != null;
        if (result.user != null) {
          await FirebaseAuth.instance.signOut();
        }
      } catch (e) {
        errors.add('فشل اختبار التسجيل المجهول: $e');
        _diagnosticResults['anonymousTest'] = false;
      }

      // اختبار إنشاء حساب تجريبي
      try {
        final testEmail =
            'test${DateTime.now().millisecondsSinceEpoch}@test.com';
        final result = await FirebaseAuth.instance
            .createUserWithEmailAndPassword(
              email: testEmail,
              password: 'test123456',
            );
        _diagnosticResults['createAccountTest'] = result.user != null;
        if (result.user != null) {
          await result.user!.delete();
        }
      } catch (e) {
        if (e.toString().contains('operation-not-allowed')) {
          errors.add('إنشاء الحسابات غير مفعل في Firebase Console');
        } else {
          errors.add('فشل اختبار إنشاء الحساب: $e');
        }
        _diagnosticResults['createAccountTest'] = false;
      }

      _diagnosticResults['signOutTest'] = true;
    } catch (e) {
      errors.add('خطأ عام في التشخيص: $e');
    }

    if (errors.isNotEmpty) {
      _diagnosticResults['errors'] = errors;
    }

    setState(() {
      _isRunning = false;
    });
  }
}
