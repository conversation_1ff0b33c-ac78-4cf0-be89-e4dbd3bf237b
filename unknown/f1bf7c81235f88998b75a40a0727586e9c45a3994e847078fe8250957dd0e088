import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../services/direct_auth_service.dart';
import '../../theme/app_theme.dart';

class DirectGuestTest extends StatefulWidget {
  const DirectGuestTest({super.key});

  @override
  State<DirectGuestTest> createState() => _DirectGuestTestState();
}

class _DirectGuestTestState extends State<DirectGuestTest> {
  String _status = 'جاهز للاختبار';
  bool _isLoading = false;
  Map<String, dynamic> _firebaseStatus = {};

  @override
  void initState() {
    super.initState();
    _updateFirebaseStatus();
  }

  void _updateFirebaseStatus() {
    setState(() {
      _firebaseStatus = DirectAuthService.getFirebaseStatus();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'اختبار تسجيل الدخول كضيف',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // حالة Firebase
            Card(
              elevation: 4,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '🔥 حالة Firebase',
                      style: GoogleFonts.cairo(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildStatusRow('مهيأ', _firebaseStatus['isInitialized'] ?? false),
                    _buildStatusRow('عدد التطبيقات', '${_firebaseStatus['appsCount'] ?? 0}'),
                    _buildStatusRow('المستخدم الحالي', _firebaseStatus['currentUser'] ?? 'لا يوجد'),
                    _buildStatusRow('ضيف', _firebaseStatus['isAnonymous'] ?? false),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // حالة الاختبار
            Card(
              elevation: 4,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '📊 حالة الاختبار',
                      style: GoogleFonts.cairo(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      _status,
                      style: GoogleFonts.cairo(fontSize: 16),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 30),

            // أزرار الاختبار
            ElevatedButton.icon(
              onPressed: _isLoading ? null : _testFirebaseInit,
              icon: const Icon(Icons.rocket_launch),
              label: Text(
                'تهيئة Firebase',
                style: GoogleFonts.cairo(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              ),
            ),
            const SizedBox(height: 16),

            ElevatedButton.icon(
              onPressed: _isLoading ? null : _testGuestLogin,
              icon: const Icon(Icons.person_add),
              label: Text(
                'تسجيل الدخول كضيف',
                style: GoogleFonts.cairo(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              ),
            ),
            const SizedBox(height: 16),

            ElevatedButton.icon(
              onPressed: _isLoading ? null : _testSignOut,
              icon: const Icon(Icons.logout),
              label: Text(
                'تسجيل الخروج',
                style: GoogleFonts.cairo(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              ),
            ),
            const SizedBox(height: 30),

            // زر العودة للشاشة الرئيسية
            if (_firebaseStatus['currentUser'] != null)
              ElevatedButton.icon(
                onPressed: () {
                  Navigator.of(context).pushReplacementNamed('/');
                },
                icon: const Icon(Icons.home),
                label: Text(
                  'الذهاب للشاشة الرئيسية',
                  style: GoogleFonts.cairo(fontSize: 16, fontWeight: FontWeight.w600),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
              ),

            if (_isLoading)
              const Padding(
                padding: EdgeInsets.all(20),
                child: Center(child: CircularProgressIndicator()),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, dynamic value) {
    Color color = Colors.grey;
    if (value is bool) {
      color = value ? Colors.green : Colors.red;
      value = value ? '✅' : '❌';
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: GoogleFonts.cairo(fontSize: 14)),
          Text(
            value.toString(),
            style: GoogleFonts.cairo(fontSize: 14, color: color, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Future<void> _testFirebaseInit() async {
    setState(() {
      _isLoading = true;
      _status = 'جاري تهيئة Firebase...';
    });

    try {
      await DirectAuthService.initializeFirebase();
      setState(() {
        _status = '✅ تم تهيئة Firebase بنجاح';
      });
    } catch (e) {
      setState(() {
        _status = '❌ فشل في تهيئة Firebase: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
      _updateFirebaseStatus();
    }
  }

  Future<void> _testGuestLogin() async {
    setState(() {
      _isLoading = true;
      _status = 'جاري تسجيل الدخول كضيف...';
    });

    try {
      final user = await DirectAuthService.signInAsGuestDirect();
      if (user != null) {
        setState(() {
          _status = '✅ تم تسجيل الدخول كضيف بنجاح!\nUID: ${user.uid}';
        });
      } else {
        setState(() {
          _status = '❌ فشل في تسجيل الدخول كضيف';
        });
      }
    } catch (e) {
      setState(() {
        _status = '❌ خطأ في تسجيل الدخول كضيف: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
      _updateFirebaseStatus();
    }
  }

  Future<void> _testSignOut() async {
    setState(() {
      _isLoading = true;
      _status = 'جاري تسجيل الخروج...';
    });

    try {
      await DirectAuthService.signOut();
      setState(() {
        _status = '✅ تم تسجيل الخروج بنجاح';
      });
    } catch (e) {
      setState(() {
        _status = '❌ فشل في تسجيل الخروج: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
      _updateFirebaseStatus();
    }
  }
}
