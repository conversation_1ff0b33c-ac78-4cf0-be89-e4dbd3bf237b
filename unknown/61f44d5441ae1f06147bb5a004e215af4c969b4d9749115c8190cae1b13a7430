import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'config/firebase_config.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  try {
    await FirebaseConfig.initialize();
    print('✅ Firebase تم تهيئته بنجاح');
  } catch (e) {
    print('❌ خطأ في تهيئة Firebase: $e');
  }
  
  runApp(const SimpleFirebaseTestApp());
}

class SimpleFirebaseTestApp extends StatelessWidget {
  const SimpleFirebaseTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'اختبار Firebase البسيط',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        fontFamily: GoogleFonts.cairo().fontFamily,
      ),
      home: const SimpleFirebaseTestScreen(),
    );
  }
}

class SimpleFirebaseTestScreen extends StatefulWidget {
  const SimpleFirebaseTestScreen({super.key});

  @override
  State<SimpleFirebaseTestScreen> createState() => _SimpleFirebaseTestScreenState();
}

class _SimpleFirebaseTestScreenState extends State<SimpleFirebaseTestScreen> {
  String _status = 'جاهز للاختبار';
  bool _isLoading = false;
  User? _currentUser;

  @override
  void initState() {
    super.initState();
    _checkFirebaseStatus();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'اختبار Firebase البسيط',
          style: GoogleFonts.cairo(),
        ),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'حالة Firebase:',
                      style: GoogleFonts.cairo(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 10),
                    Text(
                      'عدد التطبيقات: ${Firebase.apps.length}',
                      style: GoogleFonts.cairo(),
                    ),
                    Text(
                      'المستخدم الحالي: ${_currentUser?.uid ?? 'لا يوجد'}',
                      style: GoogleFonts.cairo(),
                    ),
                    Text(
                      'نوع المستخدم: ${_currentUser?.isAnonymous == true ? 'مجهول' : 'مسجل'}',
                      style: GoogleFonts.cairo(),
                    ),
                    const SizedBox(height: 10),
                    Text(
                      'الحالة: $_status',
                      style: GoogleFonts.cairo(
                        color: _status.contains('✅') ? Colors.green : 
                               _status.contains('❌') ? Colors.red : Colors.orange,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: _isLoading ? null : _testAnonymousSignIn,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: _isLoading
                  ? const CircularProgressIndicator(color: Colors.white)
                  : Text(
                      'اختبار تسجيل الدخول المجهول',
                      style: GoogleFonts.cairo(),
                    ),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _isLoading ? null : _testEmailSignUp,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: _isLoading
                  ? const CircularProgressIndicator(color: Colors.white)
                  : Text(
                      'اختبار إنشاء حساب بالإيميل',
                      style: GoogleFonts.cairo(),
                    ),
            ),
            const SizedBox(height: 10),
            if (_currentUser != null)
              ElevatedButton(
                onPressed: _isLoading ? null : _testSignOut,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: _isLoading
                    ? const CircularProgressIndicator(color: Colors.white)
                    : Text(
                        'تسجيل الخروج',
                        style: GoogleFonts.cairo(),
                      ),
              ),
          ],
        ),
      ),
    );
  }

  void _checkFirebaseStatus() {
    setState(() {
      if (Firebase.apps.isEmpty) {
        _status = '❌ Firebase غير مُهيأ';
      } else {
        _status = '✅ Firebase مُهيأ بنجاح';
        _currentUser = FirebaseAuth.instance.currentUser;
      }
    });
  }

  Future<void> _testAnonymousSignIn() async {
    setState(() {
      _isLoading = true;
      _status = 'جاري اختبار تسجيل الدخول المجهول...';
    });

    try {
      final UserCredential result = await FirebaseAuth.instance.signInAnonymously();
      setState(() {
        _currentUser = result.user;
        _status = '✅ تم تسجيل الدخول المجهول بنجاح';
      });
    } catch (e) {
      setState(() {
        _status = '❌ فشل تسجيل الدخول المجهول: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testEmailSignUp() async {
    setState(() {
      _isLoading = true;
      _status = 'جاري اختبار إنشاء حساب بالإيميل...';
    });

    try {
      final String testEmail = 'test${DateTime.now().millisecondsSinceEpoch}@test.com';
      final UserCredential result = await FirebaseAuth.instance
          .createUserWithEmailAndPassword(
        email: testEmail,
        password: 'test123456',
      );
      
      setState(() {
        _currentUser = result.user;
        _status = '✅ تم إنشاء الحساب بنجاح: $testEmail';
      });
    } catch (e) {
      setState(() {
        _status = '❌ فشل إنشاء الحساب: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testSignOut() async {
    setState(() {
      _isLoading = true;
      _status = 'جاري تسجيل الخروج...';
    });

    try {
      await FirebaseAuth.instance.signOut();
      setState(() {
        _currentUser = null;
        _status = '✅ تم تسجيل الخروج بنجاح';
      });
    } catch (e) {
      setState(() {
        _status = '❌ فشل تسجيل الخروج: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
