import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import '../config/firebase_config.dart';

class DirectAuthService {
  // تهيئة Firebase مباشرة
  static Future<void> initializeFirebase() async {
    try {
      if (Firebase.apps.isEmpty) {
        // استخدام FirebaseConfig بدلاً من التهيئة المباشرة
        await FirebaseConfig.initialize();
        // Firebase تم تهيئته بنجاح
      } else {
        // Firebase مُهيأ مسبقاً
      }
    } catch (e) {
      // خطأ في تهيئة Firebase
      rethrow;
    }
  }

  // تسجيل الدخول كضيف مباشرة
  static Future<User?> signInAsGuestDirect() async {
    try {
      // تهيئة Firebase أولاً
      await initializeFirebase();

      // بدء تسجيل الدخول كضيف

      // تسجيل الدخول كضيف
      final UserCredential userCredential =
          await FirebaseAuth.instance.signInAnonymously();

      if (userCredential.user != null) {
        // تم تسجيل الدخول كضيف بنجاح
        return userCredential.user;
      } else {
        // فشل في الحصول على بيانات المستخدم
        return null;
      }
    } catch (e) {
      // خطأ في تسجيل الدخول كضيف
      rethrow;
    }
  }

  // التحقق من حالة المستخدم
  static User? getCurrentUser() {
    return FirebaseAuth.instance.currentUser;
  }

  // تسجيل الخروج
  static Future<void> signOut() async {
    try {
      await FirebaseAuth.instance.signOut();
      // تم تسجيل الخروج بنجاح
    } catch (e) {
      // خطأ في تسجيل الخروج
      rethrow;
    }
  }

  // فحص حالة Firebase
  static Map<String, dynamic> getFirebaseStatus() {
    return {
      'isInitialized': Firebase.apps.isNotEmpty,
      'appsCount': Firebase.apps.length,
      'currentUser': FirebaseAuth.instance.currentUser?.uid,
      'isAnonymous': FirebaseAuth.instance.currentUser?.isAnonymous ?? false,
    };
  }
}
