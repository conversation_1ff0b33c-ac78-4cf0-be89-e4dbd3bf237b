import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'providers/simple_auth_provider.dart';
import 'main.dart';

class TestEmailAuth extends StatefulWidget {
  const TestEmailAuth({super.key});

  @override
  State<TestEmailAuth> createState() => _TestEmailAuthState();
}

class _TestEmailAuthState extends State<TestEmailAuth> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _nameController = TextEditingController();
  bool _isLoginMode = true;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SimpleAuthProvider>(
      builder: (context, authProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: Text(
              'اختبار تسجيل الدخول بالإيميل',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
          ),
          body: Padding(
            padding: const EdgeInsets.all(20),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'حالة المصادقة:',
                            style: GoogleFonts.cairo(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 10),
                          Text(
                            'مسجل دخول: ${authProvider.isLoggedIn ? "نعم" : "لا"}',
                            style: GoogleFonts.cairo(fontSize: 16),
                          ),
                          Text(
                            'الاسم: ${authProvider.displayName}',
                            style: GoogleFonts.cairo(fontSize: 16),
                          ),
                          if (authProvider.error != null)
                            Text(
                              'خطأ: ${authProvider.error}',
                              style: GoogleFonts.cairo(fontSize: 14, color: Colors.red),
                            ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),
                  
                  // Toggle بين تسجيل الدخول وإنشاء الحساب
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => setState(() => _isLoginMode = true),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: _isLoginMode ? Colors.blue : Colors.grey[300],
                            foregroundColor: _isLoginMode ? Colors.white : Colors.black,
                          ),
                          child: Text('تسجيل الدخول', style: GoogleFonts.cairo()),
                        ),
                      ),
                      const SizedBox(width: 10),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => setState(() => _isLoginMode = false),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: !_isLoginMode ? Colors.blue : Colors.grey[300],
                            foregroundColor: !_isLoginMode ? Colors.white : Colors.black,
                          ),
                          child: Text('إنشاء حساب', style: GoogleFonts.cairo()),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  
                  // حقول الإدخال
                  if (!_isLoginMode) ...[
                    TextFormField(
                      controller: _nameController,
                      decoration: InputDecoration(
                        labelText: 'الاسم',
                        labelStyle: GoogleFonts.cairo(),
                        border: const OutlineInputBorder(),
                      ),
                      style: GoogleFonts.cairo(),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال الاسم';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                  ],
                  
                  TextFormField(
                    controller: _emailController,
                    decoration: InputDecoration(
                      labelText: 'البريد الإلكتروني',
                      labelStyle: GoogleFonts.cairo(),
                      border: const OutlineInputBorder(),
                    ),
                    style: GoogleFonts.cairo(),
                    keyboardType: TextInputType.emailAddress,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال البريد الإلكتروني';
                      }
                      if (!value.contains('@')) {
                        return 'البريد الإلكتروني غير صحيح';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  
                  TextFormField(
                    controller: _passwordController,
                    decoration: InputDecoration(
                      labelText: 'كلمة المرور',
                      labelStyle: GoogleFonts.cairo(),
                      border: const OutlineInputBorder(),
                    ),
                    style: GoogleFonts.cairo(),
                    obscureText: true,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال كلمة المرور';
                      }
                      if (!_isLoginMode && value.length < 6) {
                        return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 30),
                  
                  // أزرار العمل
                  if (!authProvider.isLoggedIn) ...[
                    ElevatedButton(
                      onPressed: authProvider.isLoading ? null : _handleAuth,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: authProvider.isLoading
                          ? const CircularProgressIndicator(color: Colors.white)
                          : Text(
                              _isLoginMode ? 'تسجيل الدخول' : 'إنشاء الحساب',
                              style: GoogleFonts.cairo(fontSize: 16),
                            ),
                    ),
                  ] else ...[
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pushReplacement(
                          MaterialPageRoute(builder: (context) => const MainScreen()),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: Text(
                        'الذهاب للتطبيق الرئيسي',
                        style: GoogleFonts.cairo(fontSize: 16),
                      ),
                    ),
                    const SizedBox(height: 10),
                    ElevatedButton(
                      onPressed: authProvider.isLoading ? null : _handleSignOut,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: authProvider.isLoading
                          ? const CircularProgressIndicator(color: Colors.white)
                          : Text(
                              'تسجيل الخروج',
                              style: GoogleFonts.cairo(fontSize: 16),
                            ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Future<void> _handleAuth() async {
    if (!_formKey.currentState!.validate()) return;

    final authProvider = Provider.of<SimpleAuthProvider>(context, listen: false);
    bool success = false;

    if (_isLoginMode) {
      success = await authProvider.signInWithEmail(
        _emailController.text.trim(),
        _passwordController.text,
      );
    } else {
      success = await authProvider.createAccount(
        _emailController.text.trim(),
        _passwordController.text,
        _nameController.text.trim(),
      );
    }

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            success 
                ? (_isLoginMode ? 'تم تسجيل الدخول بنجاح' : 'تم إنشاء الحساب بنجاح')
                : (authProvider.error ?? 'فشل في العملية'),
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    }
  }

  Future<void> _handleSignOut() async {
    final authProvider = Provider.of<SimpleAuthProvider>(context, listen: false);
    final success = await authProvider.signOut();
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            success ? 'تم تسجيل الخروج بنجاح' : 'فشل في تسجيل الخروج',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    }
  }
}
