import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'providers/simple_auth_provider.dart';
import 'main.dart';

class TestAuthFlow extends StatefulWidget {
  const TestAuthFlow({super.key});

  @override
  State<TestAuthFlow> createState() => _TestAuthFlowState();
}

class _TestAuthFlowState extends State<TestAuthFlow> {
  @override
  Widget build(BuildContext context) {
    return Consumer<SimpleAuthProvider>(
      builder: (context, authProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: Text(
              'اختبار تدفق المصادقة',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
          ),
          body: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'حالة المصادقة:',
                          style: GoogleFonts.cairo(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 10),
                        Text(
                          'مُهيأ: ${authProvider.isInitialized ? "نعم" : "لا"}',
                          style: GoogleFonts.cairo(fontSize: 16),
                        ),
                        Text(
                          'مسجل دخول: ${authProvider.isLoggedIn ? "نعم" : "لا"}',
                          style: GoogleFonts.cairo(fontSize: 16),
                        ),
                        Text(
                          'ضيف: ${authProvider.isGuest ? "نعم" : "لا"}',
                          style: GoogleFonts.cairo(fontSize: 16),
                        ),
                        Text(
                          'الاسم: ${authProvider.displayName}',
                          style: GoogleFonts.cairo(fontSize: 16),
                        ),
                        if (authProvider.currentUserId != null)
                          Text(
                            'UID: ${authProvider.currentUserId}',
                            style: GoogleFonts.cairo(fontSize: 12, color: Colors.grey),
                          ),
                        if (authProvider.error != null)
                          Text(
                            'خطأ: ${authProvider.error}',
                            style: GoogleFonts.cairo(fontSize: 14, color: Colors.red),
                          ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 30),
                if (!authProvider.isLoggedIn) ...[
                  ElevatedButton(
                    onPressed: authProvider.isLoading ? null : _testGuestLogin,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: authProvider.isLoading
                        ? const CircularProgressIndicator(color: Colors.white)
                        : Text(
                            'تسجيل الدخول كضيف',
                            style: GoogleFonts.cairo(fontSize: 16),
                          ),
                  ),
                ] else ...[
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pushReplacement(
                        MaterialPageRoute(builder: (context) => const MainScreen()),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: Text(
                      'الذهاب للتطبيق الرئيسي',
                      style: GoogleFonts.cairo(fontSize: 16),
                    ),
                  ),
                  const SizedBox(height: 10),
                  ElevatedButton(
                    onPressed: authProvider.isLoading ? null : _testSignOut,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: authProvider.isLoading
                        ? const CircularProgressIndicator(color: Colors.white)
                        : Text(
                            'تسجيل الخروج',
                            style: GoogleFonts.cairo(fontSize: 16),
                          ),
                  ),
                ],
                const SizedBox(height: 30),
                Text(
                  'هذا اختبار لتدفق المصادقة باستخدام SimpleAuthProvider',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> _testGuestLogin() async {
    final authProvider = Provider.of<SimpleAuthProvider>(context, listen: false);
    final success = await authProvider.signInAsGuest();
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            success ? 'تم تسجيل الدخول كضيف بنجاح' : 'فشل في تسجيل الدخول كضيف',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    }
  }

  Future<void> _testSignOut() async {
    final authProvider = Provider.of<SimpleAuthProvider>(context, listen: false);
    final success = await authProvider.signOut();
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            success ? 'تم تسجيل الخروج بنجاح' : 'فشل في تسجيل الخروج',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    }
  }
}
