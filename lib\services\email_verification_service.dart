import 'dart:math';
import 'package:crypto/crypto.dart';
import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:mailer/mailer.dart';
import 'package:mailer/smtp_server.dart';
import '../config/email_config.dart';

class EmailVerificationService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // إنشاء كود تحقق من 6 أرقام
  static String generateVerificationCode() {
    final random = Random();
    return (100000 + random.nextInt(900000)).toString();
  }

  // حفظ كود التحقق في Firestore
  static Future<bool> saveVerificationCode(String email, String code) async {
    try {
      final hashedEmail = _hashEmail(email);

      await _firestore.collection('email_verifications').doc(hashedEmail).set({
        'code': code,
        'email': email,
        'createdAt': FieldValue.serverTimestamp(),
        'expiresAt': DateTime.now().add(const Duration(minutes: 10)),
        'attempts': 0,
        'verified': false,
      });

      return true;
    } catch (e) {
      return false;
    }
  }

  // التحقق من كود التحقق
  static Future<VerificationResult> verifyCode(
    String email,
    String code,
  ) async {
    try {
      final hashedEmail = _hashEmail(email);
      final doc =
          await _firestore
              .collection('email_verifications')
              .doc(hashedEmail)
              .get();

      if (!doc.exists) {
        return VerificationResult(false, 'كود التحقق غير موجود');
      }

      final data = doc.data()!;
      final storedCode = data['code'] as String;
      final expiresAt = (data['expiresAt'] as Timestamp).toDate();
      final attempts = data['attempts'] as int;
      final verified = data['verified'] as bool;

      // التحقق من انتهاء الصلاحية
      if (DateTime.now().isAfter(expiresAt)) {
        return VerificationResult(false, 'انتهت صلاحية كود التحقق');
      }

      // التحقق من عدد المحاولات
      if (attempts >= 5) {
        return VerificationResult(false, 'تم تجاوز عدد المحاولات المسموح');
      }

      // التحقق من أن الكود لم يُستخدم من قبل
      if (verified) {
        return VerificationResult(false, 'تم استخدام هذا الكود من قبل');
      }

      // زيادة عدد المحاولات
      await _firestore
          .collection('email_verifications')
          .doc(hashedEmail)
          .update({'attempts': attempts + 1});

      // التحقق من الكود
      if (storedCode == code) {
        // تحديث حالة التحقق
        await _firestore
            .collection('email_verifications')
            .doc(hashedEmail)
            .update({
              'verified': true,
              'verifiedAt': FieldValue.serverTimestamp(),
            });

        return VerificationResult(true, 'تم التحقق بنجاح');
      } else {
        return VerificationResult(false, 'كود التحقق غير صحيح');
      }
    } catch (e) {
      // خطأ في التحقق من الكود
      return VerificationResult(false, 'حدث خطأ في التحقق');
    }
  }

  // إرسال كود التحقق عبر البريد الإلكتروني
  static Future<String?> sendVerificationCode(String email) async {
    try {
      final code = generateVerificationCode();

      // للاختبار: حفظ مؤقت في الذاكرة بدلاً من Firestore
      _tempCodes[email] = {'code': code, 'timestamp': DateTime.now()};

      // إرسال البريد الإلكتروني
      final emailSent = await _sendEmailWithCode(email, code);

      if (emailSent) {
        return code; // نعيد الكود للعرض في التطبيق
      } else {
        // إذا فشل إرسال البريد، احذف الكود المؤقت
        _tempCodes.remove(email);
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  // تخزين مؤقت للاختبار
  static final Map<String, Map<String, dynamic>> _tempCodes = {};

  // إرسال البريد الإلكتروني الفعلي
  static Future<bool> _sendEmailWithCode(String email, String code) async {
    try {
      // للاختبار: إذا كانت كلمة المرور لم تُحدث، لا نرسل بريد حقيقي
      if (EmailConfig.appPassword == 'your_app_password_here') {
        // وضع الاختبار - لا نرسل بريد حقيقي
        return true; // نعتبر الإرسال ناجح للاختبار
      }

      // إعدادات SMTP للإرسال الحقيقي
      final smtpServer = gmail(
        EmailConfig.senderEmail,
        EmailConfig.appPassword,
      );

      // إنشاء الرسالة
      final message =
          Message()
            ..from = Address(EmailConfig.senderEmail, EmailConfig.senderName)
            ..recipients.add(email)
            ..subject = 'كود التحقق - تطبيق Legal 2025'
            ..html = '''
          <div dir="rtl" style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #6366f1, #3b82f6); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
              <h1 style="color: white; margin: 0; font-size: 28px;">تطبيق Legal 2025</h1>
              <p style="color: white; margin: 10px 0 0 0; opacity: 0.9;">كود التحقق من البريد الإلكتروني</p>
            </div>

            <div style="background: white; padding: 40px; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
              <h2 style="color: #1f2937; text-align: center; margin-bottom: 20px;">مرحباً بك!</h2>

              <p style="color: #4b5563; font-size: 16px; line-height: 1.6; text-align: center;">
                شكراً لك على التسجيل في تطبيق Legal 2025. يرجى استخدام كود التحقق التالي لإكمال عملية التسجيل:
              </p>

              <div style="background: #f3f4f6; border: 2px dashed #6366f1; border-radius: 10px; padding: 30px; text-align: center; margin: 30px 0;">
                <h1 style="color: #6366f1; font-size: 36px; margin: 0; letter-spacing: 8px; font-family: monospace;">$code</h1>
              </div>

              <p style="color: #6b7280; font-size: 14px; text-align: center; margin-top: 30px;">
                هذا الكود صالح لمدة 10 دقائق فقط. إذا لم تطلب هذا الكود، يرجى تجاهل هذه الرسالة.
              </p>

              <div style="border-top: 1px solid #e5e7eb; margin-top: 30px; padding-top: 20px; text-align: center;">
                <p style="color: #9ca3af; font-size: 12px; margin: 0;">
                  تطبيق Legal 2025 - منصة التعليم القانوني
                </p>
              </div>
            </div>
          </div>
        ''';

      // إرسال الرسالة
      await send(message, smtpServer);
      return true;
    } catch (e) {
      // في حالة فشل إرسال البريد، نعيد false
      return false;
    }
  }

  // حذف كود التحقق بعد انتهاء الصلاحية
  static Future<void> cleanupExpiredCodes() async {
    try {
      final now = DateTime.now();
      final query =
          await _firestore
              .collection('email_verifications')
              .where('expiresAt', isLessThan: now)
              .get();

      for (final doc in query.docs) {
        await doc.reference.delete();
      }
    } catch (e) {
      // خطأ في تنظيف الأكواد المنتهية الصلاحية
    }
  }

  // التحقق من وجود كود صالح
  static Future<bool> hasValidCode(String email) async {
    try {
      final hashedEmail = _hashEmail(email);
      final doc =
          await _firestore
              .collection('email_verifications')
              .doc(hashedEmail)
              .get();

      if (!doc.exists) return false;

      final data = doc.data()!;
      final expiresAt = (data['expiresAt'] as Timestamp).toDate();
      final verified = data['verified'] as bool;

      return !verified && DateTime.now().isBefore(expiresAt);
    } catch (e) {
      return false;
    }
  }

  // تشفير الإيميل للأمان
  static String _hashEmail(String email) {
    final bytes = utf8.encode(email.toLowerCase());
    final digest = sha256.convert(bytes);
    return digest.toString();
  }
}

class VerificationResult {
  final bool success;
  final String message;

  VerificationResult(this.success, this.message);
}
