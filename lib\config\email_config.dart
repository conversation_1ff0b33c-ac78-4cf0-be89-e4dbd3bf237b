class EmailConfig {
  // إعدادات البريد الإلكتروني
  static const String senderEmail = '<EMAIL>';
  static const String senderName = 'تطبيق Legal 2025';
  static const String appPassword = 'your_app_password_here'; // يجب تغييرها
  
  // في بيئة الإنتاج، يجب استخدام متغيرات البيئة أو Firebase Remote Config
  // للحفاظ على أمان كلمة المرور
  
  // للحصول على App Password من Gmail:
  // 1. اذهب إلى Google Account settings
  // 2. Security → 2-Step Verification
  // 3. App passwords → Generate new password
  // 4. استخدم كلمة المرور المُنشأة هنا
  
  // بدائل أخرى:
  // - استخدام SendGrid API
  // - استخدام Firebase Functions مع Nodemailer
  // - استخدام خدمات البريد الإلكتروني المدفوعة
}
