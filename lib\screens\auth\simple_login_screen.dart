import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../providers/simple_auth_provider.dart';
import '../../providers/theme_provider.dart';
import '../../theme/app_theme.dart';

class SimpleLoginScreen extends StatefulWidget {
  const SimpleLoginScreen({super.key});

  @override
  State<SimpleLoginScreen> createState() => _SimpleLoginScreenState();
}

class _SimpleLoginScreenState extends State<SimpleLoginScreen> {
  // Controllers
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _nameController = TextEditingController();
  final _verificationCodeController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  // State variables
  bool _isLoginMode = true;
  bool _obscurePassword = true;

  // ═══════════════════════════════════════════════════════════════
  // Validation Functions - دوال التحقق من صحة البيانات
  // ═══════════════════════════════════════════════════════════════

  /// التحقق من صحة الاسم
  /// - لا يمكن أن يكون فارغاً
  /// - يجب أن يكون حرفين على الأقل
  /// - لا يزيد عن 50 حرف
  String? _validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال الاسم الكامل';
    }

    final name = value.trim();

    if (name.length < 2) {
      return 'الاسم يجب أن يكون حرفين على الأقل';
    }

    if (name.length > 50) {
      return 'الاسم طويل جداً (50 حرف كحد أقصى)';
    }

    return null;
  }

  /// التحقق من صحة البريد الإلكتروني
  /// - لا يمكن أن يكون فارغاً
  /// - يجب أن يكون بصيغة صحيحة (<EMAIL>)
  /// - لا يزيد عن 254 حرف (معيار RFC)
  String? _validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال البريد الإلكتروني';
    }

    final email = value.trim();

    // التحقق من الطول (معيار RFC 5321)
    if (email.length > 254) {
      return 'البريد الإلكتروني طويل جداً';
    }

    // التحقق من الصيغة - regex محسن ومطابق للمعايير
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );

    if (!emailRegex.hasMatch(email)) {
      return 'يرجى إدخال بريد إلكتروني صحيح (مثال: <EMAIL>)';
    }

    return null;
  }

  /// التحقق من صحة كلمة المرور
  /// - لا يمكن أن تكون فارغة
  /// - للحسابات الجديدة: 6 أحرف على الأقل
  /// - لا تزيد عن 128 حرف
  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال كلمة المرور';
    }

    // قواعد إضافية للحسابات الجديدة فقط
    if (!_isLoginMode) {
      if (value.length < 6) {
        return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
      }

      if (value.length > 128) {
        return 'كلمة المرور طويلة جداً (128 حرف كحد أقصى)';
      }
    }

    return null;
  }

  // ═══════════════════════════════════════════════════════════════
  // End of Validation Functions
  // ═══════════════════════════════════════════════════════════════

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _nameController.dispose();
    _verificationCodeController.dispose();
    super.dispose();
  }

  // دالة عرض إشعار جميل بنمط iOS
  void _showBeautifulNotification(
    String title,
    String message,
    Color color,
    IconData icon,
  ) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: Icon(icon, size: 30, color: color),
                ),
                const SizedBox(height: 16),
                Text(
                  title,
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).textTheme.titleLarge?.color,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  message,
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Theme.of(context).textTheme.bodyMedium?.color,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: color,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: Text(
                      'حسناً',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor:
              themeProvider.isDarkMode
                  ? const Color(0xFF0F172A)
                  : AppTheme.backgroundColor,
          body: SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  const SizedBox(height: 60),
                  _buildHeader(),
                  const SizedBox(height: 40),
                  _buildLoginForm(),
                  const SizedBox(height: 30),
                  _buildSocialLogins(),
                  const SizedBox(height: 20),
                  _buildGuestLogin(),
                  const SizedBox(height: 20),
                  _buildToggleMode(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // شعار بسيط
        Container(
          width: 100,
          height: 100,
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
              colors: [AppTheme.primaryColor, Color(0xFF667eea)],
            ),
          ),
          child: const Icon(Icons.school, size: 50, color: Colors.white),
        ),
        const SizedBox(height: 20),
        Text(
          _isLoginMode ? 'مرحباً بعودتك!' : 'انضم إلينا!',
          style: GoogleFonts.cairo(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          _isLoginMode ? 'سجل دخولك للمتابعة' : 'أنشئ حساباً جديداً للبدء',
          style: GoogleFonts.cairo(fontSize: 16, color: Colors.grey[600]),
        ),
      ],
    );
  }

  Widget _buildLoginForm() {
    return Consumer<SimpleAuthProvider>(
      builder: (context, authProvider, child) {
        return Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Form(
            key: _formKey,
            child: Column(
              children: [
                if (!_isLoginMode) ...[
                  _buildTextField(
                    controller: _nameController,
                    label: 'الاسم الكامل',
                    icon: Icons.person_outline,
                    validator: _validateName,
                  ),
                  const SizedBox(height: 16),
                ],
                _buildTextField(
                  controller: _emailController,
                  label: 'البريد الإلكتروني',
                  icon: Icons.email_outlined,
                  keyboardType: TextInputType.emailAddress,
                  validator: _validateEmail,
                ),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: _passwordController,
                  label: 'كلمة المرور',
                  icon: Icons.lock_outline,
                  obscureText: _obscurePassword,
                  suffixIcon: IconButton(
                    icon: Icon(
                      _obscurePassword
                          ? Icons.visibility_off
                          : Icons.visibility,
                      color: Colors.grey[600],
                    ),
                    onPressed: () {
                      setState(() {
                        _obscurePassword = !_obscurePassword;
                      });
                    },
                  ),
                  validator: _validatePassword,
                ),

                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    onPressed: authProvider.isLoading ? null : _handleEmailAuth,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child:
                        authProvider.isLoading
                            ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            )
                            : Text(
                              _isLoginMode ? 'تسجيل الدخول' : 'إنشاء الحساب',
                              style: GoogleFonts.cairo(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                  ),
                ),
                if (authProvider.error != null) ...[
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.red[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.red[200]!),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.error_outline,
                          color: Colors.red[600],
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            authProvider.error!,
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color: Colors.red[700],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    bool obscureText = false,
    TextInputType keyboardType = TextInputType.text,
    String? Function(String?)? validator,
    Widget? suffixIcon,
    int? maxLength,
  }) {
    return TextFormField(
      controller: controller,
      obscureText: obscureText,
      keyboardType: keyboardType,
      validator: validator,
      maxLength: maxLength,
      style: GoogleFonts.cairo(fontSize: 16),
      decoration: InputDecoration(
        labelText: label,
        labelStyle: GoogleFonts.cairo(fontSize: 14),
        prefixIcon: Icon(icon, size: 22),
        suffixIcon: suffixIcon,
        filled: true,
        fillColor: Colors.grey[50],
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppTheme.primaryColor, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
      ),
    );
  }

  Widget _buildSocialLogins() {
    return Consumer<SimpleAuthProvider>(
      builder: (context, authProvider, child) {
        return Column(
          children: [
            Row(
              children: [
                Expanded(child: Divider(color: Colors.grey[300])),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text(
                    'أو',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
                Expanded(child: Divider(color: Colors.grey[300])),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: _buildSocialButton(
                    label: 'Google',
                    icon: Icons.g_mobiledata,
                    onPressed:
                        authProvider.isLoading
                            ? null
                            : () => _handleSocialLogin('google'),
                    backgroundColor: Colors.white,
                    textColor: Colors.grey[800]!,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildSocialButton(
                    label: 'Facebook',
                    icon: Icons.facebook,
                    onPressed:
                        authProvider.isLoading
                            ? null
                            : () => _handleSocialLogin('facebook'),
                    backgroundColor: const Color(0xFF1877F2),
                    textColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  Widget _buildSocialButton({
    required String label,
    required IconData icon,
    required VoidCallback? onPressed,
    required Color backgroundColor,
    required Color textColor,
  }) {
    return SizedBox(
      height: 50,
      child: ElevatedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon, size: 20),
        label: Text(
          label,
          style: GoogleFonts.cairo(fontSize: 14, fontWeight: FontWeight.w600),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor,
          foregroundColor: textColor,
          side: BorderSide(color: Colors.grey[300]!),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  Widget _buildGuestLogin() {
    return Consumer<SimpleAuthProvider>(
      builder: (context, authProvider, child) {
        return SizedBox(
          width: double.infinity,
          height: 50,
          child: OutlinedButton.icon(
            onPressed: authProvider.isLoading ? null : _handleGuestLogin,
            icon: const Icon(Icons.person_outline, size: 20),
            label: Text(
              'المتابعة كضيف',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppTheme.primaryColor,
              side: const BorderSide(color: AppTheme.primaryColor, width: 2),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildToggleMode() {
    return Column(
      children: [
        // أزرار إضافية لتسجيل الدخول
        if (_isLoginMode) ...[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              TextButton(
                onPressed: _showForgotPasswordDialog,
                child: Text(
                  'نسيت كلمة المرور؟',
                  style: GoogleFonts.cairo(
                    fontSize: 13,
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              TextButton(
                onPressed: _checkEmailVerification,
                child: Text(
                  'تحديث التحقق',
                  style: GoogleFonts.cairo(
                    fontSize: 13,
                    color: Colors.green,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
        ],

        // زر التبديل
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              _isLoginMode ? 'ليس لديك حساب؟' : 'لديك حساب بالفعل؟',
              style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey[600]),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _isLoginMode = !_isLoginMode;
                  // مسح الحقول عند التبديل
                  _nameController.clear();
                  _verificationCodeController.clear();

                  // مسح أخطاء المصادقة
                  final authProvider = Provider.of<SimpleAuthProvider>(
                    context,
                    listen: false,
                  );
                  authProvider.clearError();
                });
              },
              child: Text(
                _isLoginMode ? 'إنشاء حساب' : 'تسجيل الدخول',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // معالجة تسجيل الدخول بالإيميل
  Future<void> _handleEmailAuth() async {
    if (!_formKey.currentState!.validate()) return;

    final authProvider = Provider.of<SimpleAuthProvider>(
      context,
      listen: false,
    );

    // إظهار مؤشر التحميل
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );

    try {
      bool success = false;

      if (_isLoginMode) {
        // تسجيل دخول مع التحقق من تفعيل البريد
        success = await authProvider.signInWithEmailOnly(
          _emailController.text.trim(),
          _passwordController.text,
        );

        if (mounted) {
          Navigator.of(context).pop(); // إغلاق مؤشر التحميل
        }

        if (success && mounted) {
          // رسالة نجاح
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                '✅ تم تسجيل الدخول بنجاح!',
                style: GoogleFonts.cairo(),
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 1),
            ),
          );

          // لا حاجة للتنقل يدوياً - AuthWrapper سيتولى الأمر تلقائياً
        } else if (mounted) {
          // معالجة خاصة لأخطاء كود التحقق
          final error = authProvider.error ?? 'فشل تسجيل الدخول';

          {
            // عرض رسالة الخطأ العادية
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(error, style: GoogleFonts.cairo()),
                backgroundColor: Colors.red,
                duration: const Duration(seconds: 3),
              ),
            );
          }
        }
      } else {
        // إنشاء حساب جديد مع Firebase Email Verification
        final success = await authProvider.createAccount(
          _emailController.text.trim(),
          _passwordController.text,
          _nameController.text.trim(),
        );

        if (mounted) {
          Navigator.of(context).pop(); // إغلاق مؤشر التحميل
        }

        if (success && mounted) {
          // نجح إنشاء الحساب وإرسال رسالة التحقق
          _showBeautifulNotification(
            '✅ تم إنشاء الحساب بنجاح!',
            'تم إرسال رابط التحقق إلى ${_emailController.text.trim()}\n\nالرجاء التحقق من بريدك الإلكتروني ثم تسجيل الدخول.',
            Colors.green,
            Icons.check_circle,
          );

          // التبديل لوضع تسجيل الدخول
          setState(() {
            _isLoginMode = true;
          });

          // مسح حقل الاسم فقط
          _nameController.clear();
        } else if (mounted) {
          // فشل إنشاء الحساب أو الحساب موجود
          if (authProvider.error?.contains('مسجل بالفعل') == true ||
              authProvider.error?.contains('email-already-in-use') == true) {
            // إشعار جميل للحساب الموجود
            _showBeautifulNotification(
              '👋 مرحباً مرة أخرى!',
              'هذا البريد الإلكتروني مسجل بالفعل\nيمكنك تسجيل الدخول مباشرة',
              Colors.blue,
              Icons.person_outline,
            );

            // التبديل لوضع تسجيل الدخول
            setState(() {
              _isLoginMode = true;
              _nameController.clear();
            });
          } else {
            // خطأ آخر
            _showBeautifulNotification(
              '❌ فشل إنشاء الحساب',
              authProvider.error ?? 'حدث خطأ غير متوقع',
              Colors.red,
              Icons.error_outline,
            );
          }
        }
        return; // لا نريد تنفيذ باقي الكود
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // إغلاق مؤشر التحميل

        // رسالة خطأ مفصلة
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '❌ حدث خطأ: ${e.toString()}',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  // معالجة تسجيل الدخول الاجتماعي
  Future<void> _handleSocialLogin(String provider) async {
    final authProvider = Provider.of<SimpleAuthProvider>(
      context,
      listen: false,
    );

    // إظهار مؤشر التحميل
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(
                  provider == 'google'
                      ? 'جاري تسجيل الدخول بـ Google...'
                      : 'جاري تسجيل الدخول بـ Facebook...',
                  style: GoogleFonts.cairo(color: Colors.white),
                ),
              ],
            ),
          ),
    );

    try {
      bool success = false;

      switch (provider) {
        case 'google':
          success = await authProvider.signInWithGoogle();
          break;
        case 'facebook':
          success = await authProvider.signInWithFacebook();
          break;
      }

      if (mounted) {
        Navigator.of(context).pop(); // إغلاق مؤشر التحميل
      }

      if (success && mounted) {
        // رسالة نجاح
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              provider == 'google'
                  ? '✅ تم تسجيل الدخول بـ Google بنجاح!'
                  : '✅ تم تسجيل الدخول بـ Facebook بنجاح!',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );

        // الانتقال للصفحة الرئيسية
        Navigator.of(context).pushReplacementNamed('/home');
      } else if (mounted) {
        // رسالة خطأ
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              provider == 'google'
                  ? '❌ فشل تسجيل الدخول بـ Google. حاول مرة أخرى'
                  : '❌ فشل تسجيل الدخول بـ Facebook. حاول مرة أخرى',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // إغلاق مؤشر التحميل

        // رسالة خطأ مفصلة
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '❌ حدث خطأ في تسجيل الدخول: ${e.toString()}',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  // معالجة تسجيل الدخول كضيف
  Future<void> _handleGuestLogin() async {
    final authProvider = Provider.of<SimpleAuthProvider>(
      context,
      listen: false,
    );

    // إظهار مؤشر التحميل
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(
                  'جاري تسجيل الدخول كضيف...',
                  style: GoogleFonts.cairo(color: Colors.white),
                ),
              ],
            ),
          ),
    );

    try {
      // تسجيل الدخول كضيف باستخدام Firebase Anonymous Authentication
      bool success = await authProvider.signInAsGuest();

      if (mounted) {
        Navigator.of(context).pop(); // إغلاق مؤشر التحميل
      }

      if (success && mounted) {
        // رسالة نجاح
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '✅ تم تسجيل الدخول كضيف بنجاح!',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );

        // الانتقال للصفحة الرئيسية
        Navigator.of(context).pushReplacementNamed('/home');
      } else if (mounted) {
        // رسالة خطأ
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '❌ فشل تسجيل الدخول كضيف. حاول مرة أخرى',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // إغلاق مؤشر التحميل

        // رسالة خطأ مفصلة
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '❌ حدث خطأ في تسجيل الدخول كضيف: ${e.toString()}',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  // عرض حوار نسيت كلمة المرور
  void _showForgotPasswordDialog() {
    final emailController = TextEditingController();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'إعادة تعيين كلمة المرور',
              style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'أدخل بريدك الإلكتروني لإرسال رابط إعادة تعيين كلمة المرور',
                  style: GoogleFonts.cairo(),
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: emailController,
                  keyboardType: TextInputType.emailAddress,
                  decoration: InputDecoration(
                    labelText: 'البريد الإلكتروني',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('إلغاء', style: GoogleFonts.cairo()),
              ),
              ElevatedButton(
                onPressed: () async {
                  if (emailController.text.trim().isNotEmpty) {
                    Navigator.pop(context);
                    await _resetPassword(emailController.text.trim());
                  }
                },
                child: Text('إرسال', style: GoogleFonts.cairo()),
              ),
            ],
          ),
    );
  }

  // إعادة تعيين كلمة المرور
  Future<void> _resetPassword(String email) async {
    final authProvider = Provider.of<SimpleAuthProvider>(
      context,
      listen: false,
    );

    final success = await authProvider.resetPassword(email);

    if (mounted) {
      if (success) {
        _showBeautifulNotification(
          '✅ تم الإرسال بنجاح',
          'تم إرسال رابط إعادة تعيين كلمة المرور إلى $email',
          Colors.green,
          Icons.check_circle,
        );
      } else {
        _showBeautifulNotification(
          '❌ فشل الإرسال',
          authProvider.error ?? 'حدث خطأ غير متوقع',
          Colors.red,
          Icons.error_outline,
        );
      }
    }
  }

  // تحديث حالة التحقق من البريد
  Future<void> _checkEmailVerification() async {
    final authProvider = Provider.of<SimpleAuthProvider>(
      context,
      listen: false,
    );

    final isVerified = await authProvider.checkEmailVerification();

    if (mounted) {
      if (isVerified) {
        _showBeautifulNotification(
          '✅ تم التحقق بنجاح',
          'تم تفعيل بريدك الإلكتروني. يمكنك الآن تسجيل الدخول.',
          Colors.green,
          Icons.verified,
        );
      } else {
        _showBeautifulNotification(
          '⏳ لم يتم التحقق بعد',
          'يرجى التحقق من بريدك الإلكتروني أولاً، ثم اضغط على هذا الزر مرة أخرى.',
          Colors.orange,
          Icons.pending,
        );
      }
    }
  }
}
